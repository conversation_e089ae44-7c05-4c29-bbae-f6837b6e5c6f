{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 12192, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 12192, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 12192, "tid": 87107, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 12192, "tid": 87107, "ts": 1754817542823152, "dur": 2413, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 12192, "tid": 87107, "ts": 1754817542850570, "dur": 6633, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 12192, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 12192, "tid": 1, "ts": 1754817534742036, "dur": 28463, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 12192, "tid": 1, "ts": 1754817534770507, "dur": 544187, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 12192, "tid": 1, "ts": 1754817535314721, "dur": 132891, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 12192, "tid": 87107, "ts": 1754817542857212, "dur": 21, "ph": "X", "name": "", "args": {}}, {"pid": 12192, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534735777, "dur": 110083, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534845865, "dur": 7850406, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534855924, "dur": 77132, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534933074, "dur": 10347, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534943432, "dur": 10127, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534953573, "dur": 48, "ph": "X", "name": "ProcessMessages 20532", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534953624, "dur": 4076, "ph": "X", "name": "ReadAsync 20532", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534957718, "dur": 69, "ph": "X", "name": "ProcessMessages 20490", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534957790, "dur": 4524, "ph": "X", "name": "ReadAsync 20490", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534962326, "dur": 71, "ph": "X", "name": "ProcessMessages 12681", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534962405, "dur": 7893, "ph": "X", "name": "ReadAsync 12681", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534970309, "dur": 48, "ph": "X", "name": "ProcessMessages 15238", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534970361, "dur": 679, "ph": "X", "name": "ReadAsync 15238", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534971047, "dur": 55, "ph": "X", "name": "ProcessMessages 17317", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534971106, "dur": 228, "ph": "X", "name": "ReadAsync 17317", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534971345, "dur": 7, "ph": "X", "name": "ProcessMessages 1805", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534971355, "dur": 192, "ph": "X", "name": "ReadAsync 1805", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534971554, "dur": 4, "ph": "X", "name": "ProcessMessages 688", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534971561, "dur": 191, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534971758, "dur": 3, "ph": "X", "name": "ProcessMessages 732", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534971763, "dur": 205, "ph": "X", "name": "ReadAsync 732", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534971974, "dur": 3, "ph": "X", "name": "ProcessMessages 886", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534971979, "dur": 234, "ph": "X", "name": "ReadAsync 886", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534972219, "dur": 4, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534972226, "dur": 198, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534972429, "dur": 5, "ph": "X", "name": "ProcessMessages 1185", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534972437, "dur": 159, "ph": "X", "name": "ReadAsync 1185", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534972603, "dur": 5, "ph": "X", "name": "ProcessMessages 853", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534972611, "dur": 132, "ph": "X", "name": "ReadAsync 853", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534972748, "dur": 2, "ph": "X", "name": "ProcessMessages 339", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534972752, "dur": 207, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534972966, "dur": 5, "ph": "X", "name": "ProcessMessages 765", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534972974, "dur": 163, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534973142, "dur": 3, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534973148, "dur": 122, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534973275, "dur": 4, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534973282, "dur": 132, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534973502, "dur": 3, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534973509, "dur": 170, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534973684, "dur": 5, "ph": "X", "name": "ProcessMessages 1301", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534973693, "dur": 158, "ph": "X", "name": "ReadAsync 1301", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534973857, "dur": 4, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534973863, "dur": 143, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534974012, "dur": 3, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534974018, "dur": 165, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534974189, "dur": 4, "ph": "X", "name": "ProcessMessages 724", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534974203, "dur": 146, "ph": "X", "name": "ReadAsync 724", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534974356, "dur": 4, "ph": "X", "name": "ProcessMessages 579", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534974362, "dur": 131, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534974498, "dur": 5, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534974506, "dur": 168, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534974681, "dur": 4, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534974689, "dur": 142, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534974836, "dur": 5, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534974844, "dur": 133, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534974984, "dur": 2, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534974989, "dur": 141, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534975136, "dur": 4, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534975143, "dur": 129, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534975282, "dur": 3, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534975288, "dur": 141, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534975435, "dur": 3, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534975442, "dur": 365, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534975813, "dur": 5, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534975821, "dur": 183, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534976010, "dur": 7, "ph": "X", "name": "ProcessMessages 1658", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534976021, "dur": 141, "ph": "X", "name": "ReadAsync 1658", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534976169, "dur": 3, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534976175, "dur": 148, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534976329, "dur": 2, "ph": "X", "name": "ProcessMessages 249", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534976335, "dur": 144, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534976485, "dur": 5, "ph": "X", "name": "ProcessMessages 727", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534976493, "dur": 145, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534976643, "dur": 2, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534976648, "dur": 145, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534976799, "dur": 4, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534976806, "dur": 148, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534976960, "dur": 3, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534976966, "dur": 152, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534977136, "dur": 5, "ph": "X", "name": "ProcessMessages 816", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534977144, "dur": 187, "ph": "X", "name": "ReadAsync 816", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534977338, "dur": 4, "ph": "X", "name": "ProcessMessages 933", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534977345, "dur": 116, "ph": "X", "name": "ReadAsync 933", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534977467, "dur": 3, "ph": "X", "name": "ProcessMessages 515", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534977508, "dur": 151, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534977664, "dur": 3, "ph": "X", "name": "ProcessMessages 983", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534977670, "dur": 146, "ph": "X", "name": "ReadAsync 983", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534977820, "dur": 3, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534977825, "dur": 130, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534977961, "dur": 3, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534977969, "dur": 137, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534978110, "dur": 2, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534978114, "dur": 138, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534978257, "dur": 2, "ph": "X", "name": "ProcessMessages 768", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534978261, "dur": 137, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534978403, "dur": 3, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534978408, "dur": 125, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534978622, "dur": 3, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534978628, "dur": 152, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534978784, "dur": 4, "ph": "X", "name": "ProcessMessages 718", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534978792, "dur": 142, "ph": "X", "name": "ReadAsync 718", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534978939, "dur": 5, "ph": "X", "name": "ProcessMessages 889", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534978948, "dur": 852, "ph": "X", "name": "ReadAsync 889", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534979808, "dur": 6, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534979817, "dur": 3935, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534983764, "dur": 20, "ph": "X", "name": "ProcessMessages 3068", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534983787, "dur": 3129, "ph": "X", "name": "ReadAsync 3068", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534986927, "dur": 27, "ph": "X", "name": "ProcessMessages 10730", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534986956, "dur": 1018, "ph": "X", "name": "ReadAsync 10730", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534987995, "dur": 16, "ph": "X", "name": "ProcessMessages 1614", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534988015, "dur": 115, "ph": "X", "name": "ReadAsync 1614", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534988135, "dur": 10, "ph": "X", "name": "ProcessMessages 2128", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534988162, "dur": 108, "ph": "X", "name": "ReadAsync 2128", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534988276, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534988282, "dur": 92, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534988379, "dur": 3, "ph": "X", "name": "ProcessMessages 485", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534988386, "dur": 74, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534988466, "dur": 2, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534988471, "dur": 150, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534988627, "dur": 3, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534988632, "dur": 65, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534988702, "dur": 2, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534988706, "dur": 234, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534988946, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534988949, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534989013, "dur": 1, "ph": "X", "name": "ProcessMessages 222", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534989017, "dur": 66, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534989088, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534989092, "dur": 76, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534989173, "dur": 2, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534989177, "dur": 78, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534989259, "dur": 2, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534989266, "dur": 66, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534989390, "dur": 3, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534989399, "dur": 277, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534989682, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534989688, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534989782, "dur": 2, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534989786, "dur": 87, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534989879, "dur": 2, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534989882, "dur": 86, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534989973, "dur": 3, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534989978, "dur": 59, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534990042, "dur": 2, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534990046, "dur": 255, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534990306, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534990309, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534990389, "dur": 2, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534990395, "dur": 71, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534990470, "dur": 2, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534990475, "dur": 74, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534990553, "dur": 2, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534990557, "dur": 81, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534990643, "dur": 3, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534990649, "dur": 89, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534990742, "dur": 2, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534990747, "dur": 104, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534990857, "dur": 2, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534990861, "dur": 108, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534990974, "dur": 3, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534990979, "dur": 126, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534991109, "dur": 3, "ph": "X", "name": "ProcessMessages 348", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534991114, "dur": 107, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534991227, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534991232, "dur": 229, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534991466, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534991469, "dur": 108, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534991583, "dur": 3, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534991589, "dur": 123, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534991717, "dur": 3, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534991722, "dur": 132, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534991861, "dur": 4, "ph": "X", "name": "ProcessMessages 827", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534991868, "dur": 109, "ph": "X", "name": "ReadAsync 827", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534991981, "dur": 3, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534991986, "dur": 113, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534992104, "dur": 3, "ph": "X", "name": "ProcessMessages 613", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534992110, "dur": 159, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534992363, "dur": 4, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534992369, "dur": 110, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534992485, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534992491, "dur": 135, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534992631, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534992636, "dur": 121, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534992763, "dur": 2, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534992767, "dur": 109, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534992885, "dur": 4, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534992892, "dur": 116, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534993012, "dur": 6, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534993022, "dur": 919, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534993948, "dur": 5, "ph": "X", "name": "ProcessMessages 630", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534993956, "dur": 215, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534994178, "dur": 10, "ph": "X", "name": "ProcessMessages 2064", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534994191, "dur": 117, "ph": "X", "name": "ReadAsync 2064", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534994313, "dur": 4, "ph": "X", "name": "ProcessMessages 918", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534994321, "dur": 114, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534994440, "dur": 7, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534994450, "dur": 113, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534994569, "dur": 4, "ph": "X", "name": "ProcessMessages 635", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534994576, "dur": 104, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534994684, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534994689, "dur": 944, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534995641, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534995646, "dur": 764, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534996418, "dur": 10, "ph": "X", "name": "ProcessMessages 1156", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534996432, "dur": 299, "ph": "X", "name": "ReadAsync 1156", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534996738, "dur": 10, "ph": "X", "name": "ProcessMessages 2164", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817534996751, "dur": 4943, "ph": "X", "name": "ReadAsync 2164", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535001717, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535001730, "dur": 1060, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535002799, "dur": 36, "ph": "X", "name": "ProcessMessages 9842", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535002838, "dur": 204, "ph": "X", "name": "ReadAsync 9842", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535003047, "dur": 5, "ph": "X", "name": "ProcessMessages 1041", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535003054, "dur": 197, "ph": "X", "name": "ReadAsync 1041", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535003257, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535003264, "dur": 122, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535003390, "dur": 2, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535003396, "dur": 165, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535003567, "dur": 4, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535003574, "dur": 135, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535003715, "dur": 4, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535003722, "dur": 132, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535003858, "dur": 2, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535003862, "dur": 114, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535003982, "dur": 3, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535003987, "dur": 104, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535004099, "dur": 2, "ph": "X", "name": "ProcessMessages 185", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535004104, "dur": 99, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535004208, "dur": 2, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535004213, "dur": 223, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535004440, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535004445, "dur": 118, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535004570, "dur": 3, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535004575, "dur": 111, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535004694, "dur": 2, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535004700, "dur": 115, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535004822, "dur": 4, "ph": "X", "name": "ProcessMessages 637", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535004829, "dur": 117, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535004953, "dur": 3, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535004961, "dur": 149, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535005227, "dur": 4, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535005236, "dur": 123, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535005413, "dur": 5, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535005421, "dur": 234, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535005661, "dur": 2, "ph": "X", "name": "ProcessMessages 219", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535005665, "dur": 101, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535005772, "dur": 4, "ph": "X", "name": "ProcessMessages 882", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535005779, "dur": 154, "ph": "X", "name": "ReadAsync 882", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535006092, "dur": 3, "ph": "X", "name": "ProcessMessages 147", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535006100, "dur": 258, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535006456, "dur": 6, "ph": "X", "name": "ProcessMessages 1517", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535006464, "dur": 85, "ph": "X", "name": "ReadAsync 1517", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535006623, "dur": 7, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535006633, "dur": 87, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535006724, "dur": 5, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535006731, "dur": 74, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535006812, "dur": 2, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535006815, "dur": 90, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535006910, "dur": 2, "ph": "X", "name": "ProcessMessages 253", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535006916, "dur": 58, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535006978, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535006981, "dur": 234, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535007224, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535007228, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535007304, "dur": 3, "ph": "X", "name": "ProcessMessages 230", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535007310, "dur": 73, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535007387, "dur": 2, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535007391, "dur": 83, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535007479, "dur": 2, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535007484, "dur": 71, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535007559, "dur": 2, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535007564, "dur": 76, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535007644, "dur": 2, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535007648, "dur": 73, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535007725, "dur": 2, "ph": "X", "name": "ProcessMessages 279", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535007729, "dur": 89, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535007822, "dur": 1, "ph": "X", "name": "ProcessMessages 318", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535007826, "dur": 76, "ph": "X", "name": "ReadAsync 318", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535007906, "dur": 2, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535007912, "dur": 63, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535007979, "dur": 1, "ph": "X", "name": "ProcessMessages 83", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535007982, "dur": 88, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535008076, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535008081, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535008131, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535008135, "dur": 263, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535008402, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535008407, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535008494, "dur": 2, "ph": "X", "name": "ProcessMessages 445", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535008497, "dur": 74, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535008576, "dur": 2, "ph": "X", "name": "ProcessMessages 314", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535008581, "dur": 63, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535008647, "dur": 2, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535008652, "dur": 92, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535008811, "dur": 3, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535008817, "dur": 107, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535008930, "dur": 4, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535008938, "dur": 53, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535008996, "dur": 3, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535009003, "dur": 99, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535009106, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535009111, "dur": 30, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535009145, "dur": 2, "ph": "X", "name": "ProcessMessages 33", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535009151, "dur": 32, "ph": "X", "name": "ReadAsync 33", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535009185, "dur": 27, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535009214, "dur": 332, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535009553, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535009558, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535009648, "dur": 3, "ph": "X", "name": "ProcessMessages 484", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535009654, "dur": 80, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535009739, "dur": 2, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535009743, "dur": 74, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535009822, "dur": 2, "ph": "X", "name": "ProcessMessages 136", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535009825, "dur": 259, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535010090, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535010096, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535010156, "dur": 2, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535010160, "dur": 28, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535010191, "dur": 130, "ph": "X", "name": "ProcessMessages 73", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535010328, "dur": 9930, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535020267, "dur": 494, "ph": "X", "name": "ProcessMessages 19022", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535020815, "dur": 134, "ph": "X", "name": "ReadAsync 19022", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535020953, "dur": 6, "ph": "X", "name": "ProcessMessages 2674", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535020961, "dur": 238, "ph": "X", "name": "ReadAsync 2674", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535021206, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535021209, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535021286, "dur": 2, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535021290, "dur": 67, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535021359, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535021362, "dur": 46, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535021413, "dur": 48, "ph": "X", "name": "ReadAsync 99", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535021463, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535021466, "dur": 269, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535021740, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535021743, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535021793, "dur": 1, "ph": "X", "name": "ProcessMessages 355", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535021797, "dur": 48, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535021849, "dur": 2, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535021854, "dur": 2328, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535024218, "dur": 5, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535024227, "dur": 281, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535024513, "dur": 29, "ph": "X", "name": "ProcessMessages 5225", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535024605, "dur": 608, "ph": "X", "name": "ReadAsync 5225", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535025222, "dur": 5, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535025230, "dur": 176, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535025449, "dur": 8, "ph": "X", "name": "ProcessMessages 1694", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535025459, "dur": 66, "ph": "X", "name": "ReadAsync 1694", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535025532, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535025537, "dur": 239, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535025863, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535025870, "dur": 126, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535026001, "dur": 3, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535026006, "dur": 75, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535026087, "dur": 2, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535026091, "dur": 77, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535026174, "dur": 1, "ph": "X", "name": "ProcessMessages 277", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535026178, "dur": 70, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535026253, "dur": 2, "ph": "X", "name": "ProcessMessages 238", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535026257, "dur": 83, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535026346, "dur": 3, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535026352, "dur": 54, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535026410, "dur": 3, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535026416, "dur": 60, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535026480, "dur": 2, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535026484, "dur": 70, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535026560, "dur": 3, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535026566, "dur": 77, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535026649, "dur": 1, "ph": "X", "name": "ProcessMessages 178", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535026652, "dur": 65, "ph": "X", "name": "ReadAsync 178", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535026721, "dur": 3, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535026726, "dur": 283, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535027017, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535027022, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535027114, "dur": 3, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535027120, "dur": 11190, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535038338, "dur": 4, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535038346, "dur": 16430, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535054811, "dur": 350, "ph": "X", "name": "ProcessMessages 20499", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535055169, "dur": 247, "ph": "X", "name": "ReadAsync 20499", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535055422, "dur": 1734, "ph": "X", "name": "ProcessMessages 3507", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535057206, "dur": 749, "ph": "X", "name": "ReadAsync 3507", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535057966, "dur": 19974, "ph": "X", "name": "ProcessMessages 4294", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535077983, "dur": 289, "ph": "X", "name": "ReadAsync 4294", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535078280, "dur": 46, "ph": "X", "name": "ProcessMessages 3076", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535078328, "dur": 861, "ph": "X", "name": "ReadAsync 3076", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535079196, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535079200, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535079262, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535079265, "dur": 227, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535079496, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535079499, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535079549, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535079551, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535079597, "dur": 2041, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535081650, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535081655, "dur": 534, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535082197, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535082202, "dur": 72, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535082277, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535082280, "dur": 3394, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535085685, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535085693, "dur": 305, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535086006, "dur": 4, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535086012, "dur": 3529, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535089555, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535089561, "dur": 103, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535089668, "dur": 1170, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535090847, "dur": 16879, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535107737, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535107744, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535107817, "dur": 4, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535107822, "dur": 19, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535107845, "dur": 473, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535108326, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535108332, "dur": 132, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535108469, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535108472, "dur": 983, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535109464, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535109468, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535109535, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535109538, "dur": 1778, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535143727, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535143741, "dur": 101, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535143847, "dur": 21, "ph": "X", "name": "ProcessMessages 644", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535143871, "dur": 3283, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535147166, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535147171, "dur": 111, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535147288, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535147292, "dur": 170, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535147468, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535147471, "dur": 100, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535147576, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535147584, "dur": 117, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535147706, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535147709, "dur": 102, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535147817, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535147821, "dur": 133, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535147960, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535147963, "dur": 94, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535148062, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535148065, "dur": 124, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535148195, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535148198, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535148265, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535148269, "dur": 129, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535148403, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535148406, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535148501, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535148505, "dur": 269, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535148781, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535148786, "dur": 251, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535149042, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535149047, "dur": 353, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535149408, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535149413, "dur": 132, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535149576, "dur": 28, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535149609, "dur": 306, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535149924, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535149928, "dur": 134, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535150067, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535150072, "dur": 208, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535150288, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535150292, "dur": 112, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535150410, "dur": 5, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535150418, "dur": 427, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535150853, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535150858, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535150951, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535150955, "dur": 579, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535151542, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535151546, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535151628, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535151631, "dur": 444, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535152081, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535152084, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535152154, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535152158, "dur": 462, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535152717, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535152725, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535152811, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535152814, "dur": 436, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535153255, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535153259, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535153345, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535153350, "dur": 380, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535153736, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535153740, "dur": 112, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535153858, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535153862, "dur": 442, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535154310, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535154315, "dur": 111, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535154431, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535154437, "dur": 408, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535154851, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535154856, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535154902, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535154905, "dur": 3239, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535158154, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535158160, "dur": 103, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535158268, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535158273, "dur": 129, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535158408, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535158411, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535158506, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535158520, "dur": 493, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535159019, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535159023, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535159112, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535159115, "dur": 3080, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535162206, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535162211, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535162260, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535162264, "dur": 212, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535162481, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535162484, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535162541, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535162544, "dur": 250, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535162799, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535162802, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535162862, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535162866, "dur": 6476, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535169353, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535169357, "dur": 106, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535169469, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535169473, "dur": 781, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535170261, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535170265, "dur": 68, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535170339, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535170344, "dur": 29026, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535199383, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535199389, "dur": 132, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535199526, "dur": 17, "ph": "X", "name": "ProcessMessages 856", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535199547, "dur": 728, "ph": "X", "name": "ReadAsync 856", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535200283, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535200288, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535200349, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535200353, "dur": 2282, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535202645, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535202650, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535202729, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535202733, "dur": 4293, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535207037, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535207042, "dur": 108, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535207157, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535207161, "dur": 186, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535207353, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535207356, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535207432, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535207436, "dur": 317, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535207758, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535207761, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535207830, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535207833, "dur": 2328, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535210172, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535210177, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535210272, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535210276, "dur": 349, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535210631, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535210636, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535210728, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535210733, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535210815, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535210819, "dur": 65, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535210889, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535210894, "dur": 62, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535210961, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535210965, "dur": 5504, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535216481, "dur": 201, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535216689, "dur": 1846, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535218546, "dur": 5, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817535218553, "dur": 951241, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536169806, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536169812, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536169903, "dur": 2946, "ph": "X", "name": "ProcessMessages 188", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536172859, "dur": 7270, "ph": "X", "name": "ReadAsync 188", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536180138, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536180143, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536180226, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536180229, "dur": 1173, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536181413, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536181419, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536181468, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536181472, "dur": 859, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536182339, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536182344, "dur": 115, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536182465, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536182468, "dur": 1664, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536184140, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536184146, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536184224, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536184228, "dur": 1030, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536185264, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536185267, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536185330, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536185333, "dur": 1185, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536186524, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536186527, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536186589, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536186593, "dur": 1594, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536188194, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536188198, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536188263, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536188267, "dur": 814, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536189088, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536189091, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536189154, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536189157, "dur": 1525, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536190690, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536190696, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536190742, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536190746, "dur": 1403, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536192156, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536192160, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536192203, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536192206, "dur": 1042, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536193254, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536193257, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536193324, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536193328, "dur": 1605, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536194941, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536194945, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536195011, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536195014, "dur": 1752, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536196777, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536196782, "dur": 108, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536196895, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536196899, "dur": 1801, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536198712, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536198719, "dur": 119, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536198845, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536198850, "dur": 1919, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536200780, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536200785, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536200840, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536200843, "dur": 345, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536201194, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536201198, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536201234, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536201238, "dur": 1852, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536203099, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536203104, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536203174, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536203177, "dur": 1944, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536205131, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536205136, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536205211, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536205214, "dur": 278, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536205497, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536205501, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536205562, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536205566, "dur": 1260, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536206834, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536206850, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536206906, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536206909, "dur": 2293, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536209212, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536209217, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536209289, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536209293, "dur": 1329, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536210629, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536210634, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536210699, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536210702, "dur": 2825, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536213538, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536213543, "dur": 1368, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536214924, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536214930, "dur": 8155, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536223095, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536223100, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536223181, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536223185, "dur": 3539, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536226734, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536226739, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536226824, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536226827, "dur": 5235, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536232072, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536232079, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536232167, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536232171, "dur": 3779, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536235960, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536235966, "dur": 125, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536236096, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536236099, "dur": 3553, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536239663, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536239669, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536239747, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536239751, "dur": 4393, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536244155, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536244163, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536244245, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536244249, "dur": 37952, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536282212, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536282219, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536282274, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536282278, "dur": 1513, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536283800, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536283805, "dur": 98, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536283908, "dur": 4, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536283915, "dur": 2290, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536286215, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536286221, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536286273, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536286278, "dur": 2058, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536288345, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536288350, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536288447, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536288451, "dur": 1784, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536290244, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536290252, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536290299, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536290303, "dur": 4154, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536294467, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536294473, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536294524, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536294527, "dur": 170, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536294703, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536294707, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536294775, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536294779, "dur": 3988, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536298777, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536298783, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536298867, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536298872, "dur": 776, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536299655, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536299659, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536299706, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536299709, "dur": 3064, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536302783, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536302788, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536302871, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536302875, "dur": 653, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536303535, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536303540, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536303609, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536303613, "dur": 3175, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536306798, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536306804, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536306851, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536306855, "dur": 532, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536307396, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536307401, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536307481, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536307485, "dur": 3942, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536311437, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536311442, "dur": 77, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536311524, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536311528, "dur": 4133, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536315672, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536315678, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536315774, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536315778, "dur": 46884, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536362673, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536362679, "dur": 116, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536362800, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536362804, "dur": 9899, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536372716, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536372724, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536372816, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536372820, "dur": 1699, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536374532, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536374538, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536374588, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536374592, "dur": 2652, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536377254, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536377260, "dur": 122, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536377388, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536377392, "dur": 1074, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536378474, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536378478, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536378556, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536378560, "dur": 5300, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536383889, "dur": 10, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536383904, "dur": 247, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536384169, "dur": 6, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536384179, "dur": 3013, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536387202, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536387208, "dur": 112, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536387326, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536387330, "dur": 1235, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536388574, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536388580, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536388671, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536388675, "dur": 4387, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536393072, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536393078, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536393126, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536393130, "dur": 258, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536393394, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536393398, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536393462, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536393466, "dur": 307, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536393777, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536393780, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536393841, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536393846, "dur": 4269, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536398127, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536398135, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536398223, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536398230, "dur": 81, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536398320, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536398323, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536398386, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536398389, "dur": 56, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536398450, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536398453, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536398514, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536398517, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536398579, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536398582, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536398654, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536398657, "dur": 50, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536398711, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536398713, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536398797, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536398801, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536398859, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536398862, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536398918, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536398920, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536398973, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536398976, "dur": 96, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536399076, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536399079, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536399137, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536399140, "dur": 47, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536399191, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536399193, "dur": 91, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536399290, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536399293, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536399354, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536399357, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536399418, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536399421, "dur": 59, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536399484, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536399487, "dur": 80, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536399573, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536399575, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536399641, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536399644, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536399695, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536399698, "dur": 150, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536399852, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536399856, "dur": 110, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536399971, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536399975, "dur": 407, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536400405, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536400417, "dur": 203, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536400629, "dur": 5, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536400637, "dur": 198, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536400852, "dur": 7, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536400864, "dur": 196, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536401075, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536401082, "dur": 172, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536401260, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536401266, "dur": 90, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536401367, "dur": 3, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536401374, "dur": 74, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536401452, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536401457, "dur": 319, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536401784, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536401788, "dur": 73, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536401868, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536401872, "dur": 82, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536401958, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536401962, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536402032, "dur": 2, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536402037, "dur": 65, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536402106, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536402110, "dur": 84, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536402198, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536402202, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536402272, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536402277, "dur": 61, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536402342, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536402345, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536402409, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536402412, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536402472, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536402476, "dur": 60, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536402540, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536402543, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536402616, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536402620, "dur": 67, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536402692, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536402695, "dur": 81, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536402780, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536402783, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536402841, "dur": 4, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536402847, "dur": 51, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536402901, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536402904, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536402962, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536402967, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536403031, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536403035, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536403099, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536403102, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536403167, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536403171, "dur": 63, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536403241, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536403245, "dur": 78, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536403329, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536403332, "dur": 75, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536403412, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536403417, "dur": 74, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536403497, "dur": 2, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536403501, "dur": 126, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536403633, "dur": 3, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536403638, "dur": 64, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536403707, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536403715, "dur": 60, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536403779, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536403781, "dur": 80, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536403866, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536403870, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536403936, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536403939, "dur": 97, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536404043, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536404046, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536404111, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536404118, "dur": 68, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536404191, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536404194, "dur": 109, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536404307, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536404312, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536404382, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536404386, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536404448, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536404452, "dur": 60, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536404515, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536404518, "dur": 62, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536404585, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536404588, "dur": 56, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536404649, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536404651, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536404711, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536404715, "dur": 79, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536404800, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536404803, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536404887, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536404892, "dur": 61, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536404956, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536404960, "dur": 61, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536405025, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536405028, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536405095, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536405100, "dur": 68, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536405173, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536405178, "dur": 75, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536405258, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536405261, "dur": 72, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536405338, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536405342, "dur": 73, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536405420, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536405425, "dur": 73, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536405504, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536405509, "dur": 75, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536405590, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536405593, "dur": 3860, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536409464, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536409470, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536409566, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817536409572, "dur": 4708210, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817541117792, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817541117798, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817541117859, "dur": 36, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817541117899, "dur": 105912, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817541223823, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817541223828, "dur": 92, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817541223928, "dur": 3, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817541223935, "dur": 394760, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817541618707, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817541618712, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817541618773, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817541618779, "dur": 2645, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817541621437, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817541621446, "dur": 817, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817541622275, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817541622283, "dur": 899888, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542522182, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542522188, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542522251, "dur": 31, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542522286, "dur": 21219, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542543517, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542543522, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542543577, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542543580, "dur": 29732, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542573323, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542573330, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542573398, "dur": 37, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542573438, "dur": 28977, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542602432, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542602438, "dur": 313, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542602758, "dur": 23, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542602787, "dur": 5472, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542608270, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542608276, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542608334, "dur": 56, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542608395, "dur": 30413, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542638821, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542638827, "dur": 155, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542638990, "dur": 4, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542638997, "dur": 3648, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542642661, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542642668, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542642730, "dur": 56, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542642810, "dur": 6077, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542648925, "dur": 24, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542648956, "dur": 312, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542649285, "dur": 38603, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 12192, "tid": 12884901888, "ts": 1754817542687905, "dur": 6646, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 12192, "tid": 87107, "ts": 1754817542857237, "dur": 18243, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 12192, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 12192, "tid": 8589934592, "ts": 1754817534726826, "dur": 720878, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 12192, "tid": 8589934592, "ts": 1754817535447709, "dur": 10, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 12192, "tid": 8589934592, "ts": 1754817535447721, "dur": 3713, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 12192, "tid": 87107, "ts": 1754817542875486, "dur": 24, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 12192, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 12192, "tid": 4294967296, "ts": 1754817534679790, "dur": 8059992, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 12192, "tid": 4294967296, "ts": 1754817534692813, "dur": 21436, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 12192, "tid": 4294967296, "ts": 1754817542740001, "dur": 66166, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 12192, "tid": 4294967296, "ts": 1754817542791382, "dur": 2374, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 12192, "tid": 4294967296, "ts": 1754817542806920, "dur": 48, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 12192, "tid": 87107, "ts": 1754817542875515, "dur": 55, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1754817534833820, "dur": 189, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754817534834126, "dur": 3802, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754817534837954, "dur": 3081, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754817534841447, "dur": 264, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1754817534841712, "dur": 466, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754817534842269, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ScriptAssemblies"}}, {"pid": 12345, "tid": 0, "ts": 1754817534842395, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.AdaptivePerformanceModule.dll_AD4ABE016405E9EC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534842782, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_9C727021AC569070.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534843755, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SpriteMaskModule.dll_B812FDC44B22C083.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534844281, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIAutomationModule.dll_52F1D88840AB6BCD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534845256, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_D728BD1B62B4CC1C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534845929, "dur": 32150, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_30D61BF1868D39BE.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534878605, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_B480A183C8985E4E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534878985, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_0939C2A13B34B768.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534879289, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_CEC71CF29E56A73C.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534880326, "dur": 88, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VehiclesModule.dll_8243DE1BF1FD10D4.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534880740, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_1D8000F582EDC182.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534881198, "dur": 3099, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Common.dll_1EFC6EA74EB56B96.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534884313, "dur": 170, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.Xcode.dll_34170FFEADDCB616.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534884495, "dur": 187, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_5001ACC648CFBA23.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534884735, "dur": 163, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.iOS.Extensions.dll_05415FEA72405DAF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534885093, "dur": 181, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_0854A01FF08BC637.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534885639, "dur": 189, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_715C92D4D50EF494.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534885999, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534886090, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754817534886172, "dur": 58341, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534944722, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534945022, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534945116, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534945419, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534946745, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_84A4293DBDD182DB.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534947041, "dur": 301, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534948115, "dur": 188, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ConversionSystem.ref.dll_D58562F0DF82BCBA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534948326, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534948402, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754817534948472, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534948533, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754817534948665, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534948747, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534949347, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534951041, "dur": 4183, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754817534955723, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534956412, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PerformanceTesting.ref.dll_F99DC928B1D72E5E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534958275, "dur": 368, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ref.dll_7989A1C8D57EF9BF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534958833, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754817534958975, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534959176, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ResourceManager.ref.dll_56EDCE417F1B8EC7.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534959243, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ResourceManager.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534959428, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ResourceManager.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754817534959576, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ResourceManager.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534959722, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ResourceManager.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534962520, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534962735, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Core.Editor.ref.dll_EDC8690F57C5BDFD.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534962794, "dur": 553, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534963536, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754817534963669, "dur": 416, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534964509, "dur": 70, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754817534964604, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534964699, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534964920, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.Editor.ref.dll_159E061D77A10B86.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534964990, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534965317, "dur": 174, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754817534966089, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754817534966191, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534966289, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534966951, "dur": 113, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.GPUDriven.Runtime.ref.dll_94F5458D7254E99F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534967108, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534967845, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754817534967985, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534968086, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534968519, "dur": 252, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipeline.Universal.ShaderLibrary.ref.dll_5436F99F8BDCED8B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534968944, "dur": 150, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534969114, "dur": 110, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754817534969246, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534969352, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754817534969433, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534969541, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534970028, "dur": 176, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Runtime.Shared.ref.dll_547B576A1A56EABF.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534970219, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534970299, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754817534970362, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534970432, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754817534970501, "dur": 132, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534970859, "dur": 455, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Config.Runtime.ref.dll_5D92000A132D4840.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534971329, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534971494, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754817534971655, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534971851, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.Editor.ref.dll_6AF951BE66A4493E.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534971916, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534971973, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754817534972056, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534972269, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534972444, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_D91762B7076BE462.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534972505, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534972623, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534972829, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534972997, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Editor.ref.dll_46EB9B6930B27991.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534973133, "dur": 110, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754817534973400, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534973724, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534973898, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754817534974006, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534974071, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534974271, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534974412, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534974587, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534974669, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534974907, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754817534975111, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534975394, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534975494, "dur": 132, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754817534975672, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534975747, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754817534975862, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534976061, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534976216, "dur": 100, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754817534976339, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534976401, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534976562, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_9A20CFD48BC11F43.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534976631, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534976748, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534976884, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754817534976961, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534977198, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Editor.Shared.ref.dll_FAA396FB17B8C1A8.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534977277, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534977517, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534977706, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.2D.Runtime.ref.dll_D60199621A6AEC70.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534977779, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534977921, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534978075, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534978157, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534978377, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534978520, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534978667, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534978745, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534978962, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534979057, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754817534979189, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754817534979277, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534979343, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534979585, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.TestRunner.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817534980077, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534980214, "dur": 143, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rider.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817534980366, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754817534980437, "dur": 301, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534980919, "dur": 111, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534981276, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534981829, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534983181, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817534983731, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534984176, "dur": 552, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754817534984769, "dur": 137, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534984924, "dur": 132, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534986671, "dur": 189, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817534986875, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754817534986943, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534987023, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17346584914308636752.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534987559, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817534987678, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534988196, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534988603, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817534988664, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754817534988751, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534988853, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13909235412005675431.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534989279, "dur": 122, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817534989492, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534989583, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534990004, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PlasticSCM.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817534990070, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Profiling.Core.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754817534990165, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Profiling.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534990274, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3232911574759799904.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534990637, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Profiling.Core.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817534990702, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754817534990794, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534990881, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534990964, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754817534991048, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534991130, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754817534991241, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534991800, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817534991905, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754817534991964, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534992060, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534992122, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754817534992284, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754817534992380, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817534992588, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534992970, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817534993033, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754817534993112, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534993213, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534993276, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754817534993418, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754817534994152, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754817534994234, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534994318, "dur": 173, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534994609, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754817534994805, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534995397, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817534995573, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ScriptableBuildPipeline.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534996062, "dur": 96, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ScriptableBuildPipeline.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817534996213, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534996733, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534997204, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ShaderGraph.Utilities.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817534997335, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534998407, "dur": 99, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RTLTMPro.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534999346, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14402696373757716438.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817534999848, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535000610, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754817535001034, "dur": 118, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817535002358, "dur": 570, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.CollabProxy.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817535002947, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754817535003045, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535003588, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817535003654, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754817535003730, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535003797, "dur": 106, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535003916, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754817535003987, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817535004115, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754817535004270, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817535004780, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.DocCodeSamples.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817535004846, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754817535004921, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535004991, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535005126, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817535005781, "dur": 106, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InputSystem.ForUI.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817535005994, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535006062, "dur": 123, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535006194, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754817535006556, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11294775302662683298.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817535007016, "dur": 75, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754817535007100, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535007196, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14814235663552238418.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817535007556, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817535007799, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535007883, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754817535007963, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817535008042, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754817535008122, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817535008217, "dur": 74, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817535008736, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817535008798, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754817535008883, "dur": 86, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535008978, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535009060, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754817535009174, "dur": 59, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817535009354, "dur": 93, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817535009867, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817535009984, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ResourceManager.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535010052, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12743953686143551850.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817535010414, "dur": 68, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ResourceManager.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817535010490, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ScriptableBuildPipeline.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754817535010571, "dur": 111, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ScriptableBuildPipeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535010692, "dur": 113, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/7902948547077102519.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817535011218, "dur": 73, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754817535011299, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535012260, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535012319, "dur": 78, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10571807241835812913.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817535012753, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RTLTMPro-Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535012810, "dur": 76, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535013722, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/RTLTMPro-Tests.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817535013844, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535014894, "dur": 66, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10654825778349625970.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817535015423, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535015532, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754817535015649, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1754817535016460, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535017401, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3388064732626934676.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817535018014, "dur": 111, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817535018153, "dur": 254, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754817535018845, "dur": 132, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535018997, "dur": 135, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817535020120, "dur": 554, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817535020731, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SmartVertex.Tools.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535021527, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/SmartVertex.Tools.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817535021631, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Addressables.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535022077, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Addressables.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817535022146, "dur": 67, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754817535022228, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535023172, "dur": 206, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535023904, "dur": 104, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817535024495, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535025007, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535025497, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754817535025561, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535025618, "dur": 105, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817535026017, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.State.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817535026071, "dur": 214, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/RTLTMPro-Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754817535026298, "dur": 122, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RTLTMPro-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535026472, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535026549, "dur": 89, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754817535026646, "dur": 61, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817535026773, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1754817535027345, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/RTLTMPro-Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817535027468, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SmartVertex.EditorTools.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535027526, "dur": 79, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SmartVertex.EditorTools.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535027612, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SmartVertex.EditorTools.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1754817535028019, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17515686039452104099.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817535028656, "dur": 60, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.DocExampleCode.Editor.Tests.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817535029359, "dur": 71, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535029788, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535030273, "dur": 51, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535030331, "dur": 157, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535031349, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535031844, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535032349, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535032888, "dur": 53, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535033420, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535033805, "dur": 157, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817535034554, "dur": 113, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817535036072, "dur": 63, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1754817535036144, "dur": 65, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Editor.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754817535036241, "dur": 105, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535036361, "dur": 126, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1754817535036892, "dur": 2434, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/PPv2URPConverters.dll"}}, {"pid": 12345, "tid": 0, "ts": 1754817535039339, "dur": 62, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 0, "ts": 1754817535039409, "dur": 106, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1754817534842248, "dur": 197978, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754817535040263, "dur": 7603539, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754817542643814, "dur": 53, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754817542643868, "dur": 99, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754817542643967, "dur": 50, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754817542644891, "dur": 666, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754817542648404, "dur": 112, "ph": "X", "name": "EmitBuildFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754817542648404, "dur": 113, "ph": "X", "name": "EmitBuildFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754817542648518, "dur": 9760, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1754817534851244, "dur": 189051, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535040361, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535040520, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_145E12438C7FA2B9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535040585, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535041059, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535041158, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535041381, "dur": 467, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_B8C16B0C4D66D9CE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535041852, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_E5313D8A9F3E00D1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535042681, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535043018, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535043267, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535043575, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535044098, "dur": 334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535044454, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754817535046335, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535046636, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535046742, "dur": 8208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754817535054951, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535055474, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535055639, "dur": 211, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1754817535055871, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535055949, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535056084, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535056170, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535056295, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535056676, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754817535056847, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535057665, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535057750, "dur": 269, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754817535058021, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754817535058103, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535058173, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754817535058233, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535058339, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535058527, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535058611, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535058689, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535058757, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535058866, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535059069, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535059148, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535059235, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535059565, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535059628, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535059687, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535059749, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535059807, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535060080, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535060157, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535060329, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.DocExampleCode.Editor.Tests.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754817535060393, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535060701, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535060843, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535060942, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535061096, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10242357726576751070.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754817535061247, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535061432, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8908569252755618054.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754817535061497, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535062025, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535062740, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12248871281822510316.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1754817535063120, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535063480, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535063909, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535064231, "dur": 372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535064603, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535064971, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535065307, "dur": 319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535065627, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535066040, "dur": 1899, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@c58b4ee65782\\Editor\\Manipulators\\Sequence\\SelectAndMoveItem.cs"}}, {"pid": 12345, "tid": 1, "ts": 1754817535065973, "dur": 2634, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535068610, "dur": 823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535069557, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535070318, "dur": 767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535071085, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535071696, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535073064, "dur": 927, "ph": "X", "name": "File", "args": {"detail": "D:\\Unity\\Editors\\6000.1.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Components.Server.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754817535072470, "dur": 1800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535074273, "dur": 456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535074731, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535074802, "dur": 1238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Profiling.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754817535076042, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535076266, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535076373, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ResourceManager.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535076808, "dur": 2919, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ResourceManager.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754817535079728, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535079941, "dur": 568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535080511, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535080605, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535080881, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535080945, "dur": 1171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754817535082126, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535082266, "dur": 12528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535094796, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535094879, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535094968, "dur": 1631, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754817535096601, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535096863, "dur": 1963, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754817535098828, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535099071, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535099502, "dur": 207, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535099745, "dur": 12063, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754817535111818, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535112026, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535112127, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535112567, "dur": 263, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535112831, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535112944, "dur": 11757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754817535124703, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535124998, "dur": 377, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535125433, "dur": 1569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754817535127003, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535127225, "dur": 1023, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754817535128249, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535128387, "dur": 18903, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754817535147293, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535147445, "dur": 268, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754817535147731, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535147795, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.State.ref.dll_D91762B7076BE462.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535147940, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535148038, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.TextMeshPro.ref.dll_84A4293DBDD182DB.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535148098, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Addressables.ref.dll_E8DB4810C9946D63.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535148199, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535148293, "dur": 119, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Addressables.ref.dll_E8DB4810C9946D63.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535148427, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535148529, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ScriptableBuildPipeline.Editor.ref.dll_4B3FCA9E6B3358CE.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535148662, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535148744, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ConversionSystem.ref.dll_D58562F0DF82BCBA.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535148869, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535149083, "dur": 442, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.ref.dll_407F07063643C512.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535149565, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535149705, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.2D.Runtime.ref.dll_D60199621A6AEC70.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535150075, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535150243, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Core.Editor.Shared.ref.dll_FAA396FB17B8C1A8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535150418, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Editor.ref.dll_E2F92DAB6C167CC9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535150488, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535150615, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Editor.ref.dll_E2F92DAB6C167CC9.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535150774, "dur": 389, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535151164, "dur": 142, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Mathematics.Editor.ref.dll_9A20CFD48BC11F43.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535151313, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535151790, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535151888, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535152338, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535152431, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535152881, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535152971, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535153498, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535153602, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535153966, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535154053, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535154209, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535154534, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535154602, "dur": 170, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535154777, "dur": 325, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535155103, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535155194, "dur": 24079, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754817535179274, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535179438, "dur": 1330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754817535180770, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535180908, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535181246, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535181325, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535181508, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535181576, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535181753, "dur": 432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535182187, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535182515, "dur": 559, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535183076, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535183162, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535183442, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535183506, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535183789, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535183862, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535184156, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535184219, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535184565, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535184637, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535184987, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535185074, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535185455, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535185526, "dur": 401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535185928, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535185993, "dur": 406, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535186400, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535186464, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535186833, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535186915, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.DocExampleCode.Editor.Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535187215, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535187279, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535187585, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535187659, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535187960, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535188019, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/SmartVertex.EditorTools.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1754817535188399, "dur": 844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754817535189244, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535189415, "dur": 823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754817535190239, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535190395, "dur": 781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754817535191177, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535191320, "dur": 866, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754817535192187, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535192341, "dur": 842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754817535193185, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535193321, "dur": 862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754817535194183, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535194364, "dur": 864, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754817535195230, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535195438, "dur": 832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754817535196272, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535196436, "dur": 815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754817535197254, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535197422, "dur": 1324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754817535198748, "dur": 387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535199155, "dur": 1264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754817535200422, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535200623, "dur": 2152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754817535202777, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535202977, "dur": 7800, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1754817535210778, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535210984, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535211089, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535211313, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535212000, "dur": 987, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535214467, "dur": 712, "ph": "X", "name": "File", "args": {"detail": "D:\\Unity\\Editors\\6000.1.0f1\\Editor\\Data\\Tools\\Compilation\\Unity.ILPP.Runner\\api-ms-win-core-console-l1-1-0.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754817535212988, "dur": 2192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535215181, "dur": 64, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535215442, "dur": 189, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535216163, "dur": 53, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535216320, "dur": 77, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535216409, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535216719, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualStudio.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754817535216803, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535217041, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535217212, "dur": 132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535218842, "dur": 233927, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535455811, "dur": 341, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "D:/Unity/Editors/6000.1.0f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 1, "ts": 1754817535456154, "dur": 2501, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "D:/Unity/Editors/6000.1.0f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 1, "ts": 1754817535458656, "dur": 163, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "D:/Unity/Editors/6000.1.0f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 1, "ts": 1754817535452770, "dur": 6060, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817535458831, "dur": 717706, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536176539, "dur": 4601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754817536181143, "dur": 368, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536181553, "dur": 3938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754817536185492, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536185623, "dur": 3718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754817536189343, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536189450, "dur": 4045, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754817536193496, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536193613, "dur": 5230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754817536198849, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536199036, "dur": 4298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754817536203336, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536203450, "dur": 3585, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754817536207037, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536207168, "dur": 3692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Profiling.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754817536210862, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536210988, "dur": 64467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754817536275458, "dur": 7045, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536282529, "dur": 3747, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754817536286278, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536286565, "dur": 3816, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754817536290383, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536290628, "dur": 4073, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754817536294703, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536294818, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754817536294888, "dur": 4023, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754817536298913, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536299126, "dur": 3810, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754817536302938, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536303111, "dur": 3846, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754817536306959, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536307121, "dur": 67545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754817536374680, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536374858, "dur": 3807, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RTLTMPro-Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754817536378667, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536378825, "dur": 4400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754817536383227, "dur": 466, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536383697, "dur": 433, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754817536384208, "dur": 4549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754817536388759, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536388889, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754817536389040, "dur": 4150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754817536393192, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536393390, "dur": 4983, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754817536398374, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536398510, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.TextMeshPro.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754817536398575, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536398762, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536399047, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536399163, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536399218, "dur": 95, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Mathematics.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754817536399337, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536399456, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754817536399573, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536399656, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536399709, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipeline.Universal.ShaderLibrary.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754817536399842, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536399965, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.ShaderLibrary.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754817536400033, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536400301, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754817536400358, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536400490, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754817536400591, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536400726, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536400823, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536400984, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536401078, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536401234, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536401341, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536401439, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536401621, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Multiplayer.Center.Common.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754817536402426, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536403642, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536403975, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536404095, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1754817536404200, "dur": 528, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536404776, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536404925, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536405040, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1754817536405207, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817536405855, "dur": 4717333, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817541123222, "dur": 486566, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\SmartVertex.Tools.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754817541123190, "dur": 490231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SmartVertex.Tools.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754817541618300, "dur": 653, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1754817541640902, "dur": 932781, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/SmartVertex.Tools.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1754817542597552, "dur": 41536, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\SmartVertex.Tools.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754817542597533, "dur": 41558, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/SmartVertex.Tools.dll"}}, {"pid": 12345, "tid": 1, "ts": 1754817542639147, "dur": 3360, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/SmartVertex.Tools.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754817534851365, "dur": 188962, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535040361, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535040482, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_2E5F03CD10620934.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754817535040579, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535040778, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535041083, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535041695, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535042012, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_8AEA7B1CC3A388D2.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754817535042680, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_D3222F1B425A24B0.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754817535043018, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535043267, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535043366, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535043431, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreBusinessMetricsModule.dll_2D35B77B8A4D18E6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754817535043707, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535044380, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535044860, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_0854A01FF08BC637.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754817535045164, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754817535045342, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754817535045931, "dur": 3542, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535049489, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754817535049541, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535049692, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535049851, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754817535050014, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535050089, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754817535050221, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535050344, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535050414, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535050512, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535050597, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754817535050668, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535050878, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535051029, "dur": 1855, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535052905, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754817535053149, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535053384, "dur": 1331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535054728, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754817535054803, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535054922, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535055004, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754817535055226, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535055625, "dur": 217, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754817535055866, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535055925, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754817535056033, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535056147, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535056536, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754817535056767, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535056825, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535057158, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535057382, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535057633, "dur": 340, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/RTLTMPro.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1754817535057977, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754817535058057, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535058123, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754817535058277, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535058434, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535058493, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754817535058590, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535058672, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535058835, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535059011, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535059148, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535059262, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535059322, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535059668, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535059770, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535059900, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535060039, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535060128, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535060302, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535060362, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754817535060414, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535060642, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754817535060698, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535060919, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535061041, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535061161, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535061453, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535061650, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535061760, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10010842633742469623.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754817535061856, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17346584914308636752.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754817535062068, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535062420, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535062477, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16285879237017002407.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1754817535062629, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535063125, "dur": 380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535063749, "dur": 803, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.addressables@16eddacf275e\\Tests\\Editor\\DocExampleCode\\LoadLocation.cs"}}, {"pid": 12345, "tid": 2, "ts": 1754817535063505, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535064636, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535064987, "dur": 369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535065356, "dur": 335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535065691, "dur": 450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535066142, "dur": 338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535066481, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535066890, "dur": 1409, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535068301, "dur": 780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535069082, "dur": 1070, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535070153, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535070779, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535071539, "dur": 577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535072124, "dur": 1089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535073216, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754817535073565, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535073627, "dur": 2846, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754817535076475, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535076703, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754817535077036, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535077141, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754817535077489, "dur": 1838, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754817535079328, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535079565, "dur": 2319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754817535081885, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535082127, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754817535082192, "dur": 978, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754817535083172, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535083403, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754817535083731, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535083792, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754817535084207, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535084308, "dur": 29420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754817535113731, "dur": 583, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535114437, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754817535114855, "dur": 5972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754817535120829, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535121008, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754817535121297, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535121370, "dur": 4145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754817535125517, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535125811, "dur": 373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ScriptableBuildPipeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754817535126185, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535126252, "dur": 355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754817535126664, "dur": 294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754817535127013, "dur": 1240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754817535128255, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535128397, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754817535128787, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535128891, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754817535129174, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535129276, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754817535129556, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535129632, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754817535129901, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535129963, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754817535130219, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535130291, "dur": 1275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754817535131568, "dur": 203, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535131789, "dur": 2014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754817535133805, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535133953, "dur": 881, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754817535134836, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535135022, "dur": 2058, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754817535137091, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535137308, "dur": 24975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754817535162285, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535162532, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535162596, "dur": 491, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754817535163088, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535163166, "dur": 7220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754817535170388, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535170604, "dur": 932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754817535171538, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535171718, "dur": 2246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754817535173966, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535174142, "dur": 5176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754817535179319, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535179509, "dur": 417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/RTLTMPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1754817535179987, "dur": 1229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RTLTMPro.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754817535181218, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535181417, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535181597, "dur": 2115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754817535184591, "dur": 193, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817535186001, "dur": 5932131, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/SmartVertex.Tools.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754817541124106, "dur": 99849, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\SmartVertex.Tools.ref.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754817541123173, "dur": 100827, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/SmartVertex.EditorTools.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1754817541224001, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817541224110, "dur": 384566, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\SmartVertex.Tools.dll"}}, {"pid": 12345, "tid": 2, "ts": 1754817541224104, "dur": 388106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/SmartVertex.EditorTools.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754817541618975, "dur": 590, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817541619594, "dur": 56, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1754817541620773, "dur": 901759, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/SmartVertex.EditorTools.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1754817542543564, "dur": 206, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\SmartVertex.EditorTools.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1754817542543538, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/SmartVertex.EditorTools.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1754817542543832, "dur": 99817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817534851396, "dur": 188951, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817535040366, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_D728BD1B62B4CC1C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754817535040475, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817535040739, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_69D1E9511A66BB3B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754817535040833, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817535041082, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817535041452, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817535041708, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817535041953, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817535042480, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817535043002, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_836646C75BC63239.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754817535043367, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817535043696, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817535043794, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817535044499, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754817535044853, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817535044924, "dur": 11796, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754817535056721, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817535056883, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817535056963, "dur": 386, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754817535057350, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817535057413, "dur": 11570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754817535068986, "dur": 392, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817535069417, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817535069551, "dur": 456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754817535070008, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817535070108, "dur": 2277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754817535072386, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817535072622, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817535072708, "dur": 513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754817535073222, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817535073296, "dur": 3327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754817535076624, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817535076830, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754817535077243, "dur": 4342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754817535081587, "dur": 537, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817535082190, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817535082260, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1754817535082547, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817535082616, "dur": 876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1754817535083494, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817535083652, "dur": 2040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1754817535085694, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817535085796, "dur": 315, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817535086804, "dur": 1083326, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1754817536176526, "dur": 3749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536180277, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817536180471, "dur": 3863, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ScriptableBuildPipeline.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536184336, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817536184483, "dur": 3941, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536188425, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817536188542, "dur": 3848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536192392, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817536192508, "dur": 4199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536196709, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817536196944, "dur": 4031, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536200977, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817536201106, "dur": 4230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536205338, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817536205469, "dur": 3842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536209313, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817536209567, "dur": 4524, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Addressables.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536214093, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817536214207, "dur": 9200, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Addressables.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536223415, "dur": 3429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536226847, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817536227046, "dur": 5150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536232212, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817536232393, "dur": 3701, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536236096, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817536236249, "dur": 198, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536236456, "dur": 3348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536239807, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817536239990, "dur": 4299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RTLTMPro.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536244291, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817536244466, "dur": 39511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536283979, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817536284107, "dur": 244, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536284358, "dur": 4142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ScriptableBuildPipeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536288502, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817536288658, "dur": 230, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ScriptableBuildPipeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536288895, "dur": 5704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536294601, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817536294753, "dur": 63163, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536357925, "dur": 4534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536362462, "dur": 492, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817536362955, "dur": 302, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536363265, "dur": 8718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Addressables.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536371986, "dur": 1006, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817536373036, "dur": 4364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536377402, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817536377553, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536377733, "dur": 9595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536387330, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817536387497, "dur": 179, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536387685, "dur": 5862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536393548, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817536393751, "dur": 15868, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1754817536409621, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817536409844, "dur": 6133705, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1754817542543590, "dur": 199, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\SmartVertex.EditorTools.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754817542543550, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/SmartVertex.EditorTools.dll"}}, {"pid": 12345, "tid": 3, "ts": 1754817542543849, "dur": 99698, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817534851429, "dur": 189045, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535040477, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_0A80E49C77D34139.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754817535040647, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535040734, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_B499D9097B096213.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754817535040924, "dur": 480, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_30D61BF1868D39BE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754817535041678, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535041858, "dur": 720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_734B28A34D825150.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754817535042983, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_A3306460A01459EE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754817535043424, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.BuildProfileModule.dll_A250E997A437A30D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754817535044399, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535044665, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535044861, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_A8DBA71237A1D37F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754817535045365, "dur": 178, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535045554, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754817535045604, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535045825, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754817535045923, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754817535046122, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535046180, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754817535046647, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754817535047070, "dur": 2136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535049226, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_EC1F0C20321316E3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754817535049283, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535049370, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535049502, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754817535049589, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535049707, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754817535049784, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535049867, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535049933, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535050063, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535050215, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535050514, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535050613, "dur": 701, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535051389, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535051629, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535051818, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535051983, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535052165, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535052535, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535052700, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535052775, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535052857, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535052923, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754817535053027, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535053193, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535053294, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535053530, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535053654, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754817535054091, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754817535054268, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535054404, "dur": 367, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535054809, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535055229, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535055366, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535055617, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754817535055826, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535055894, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754817535055949, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535056077, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754817535056150, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535056237, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535056314, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535056559, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535056720, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754817535056896, "dur": 833, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535057730, "dur": 282, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1754817535058015, "dur": 104, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754817535058120, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535058181, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754817535058261, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535058382, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535058565, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535058690, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535058800, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535058862, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535058983, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535059272, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535059330, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535059553, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535059716, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535059832, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535059931, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535059990, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535060179, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535060319, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535060425, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535060506, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535060667, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535060724, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754817535060808, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535060919, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535060991, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5249134988916615986.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754817535061065, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535062066, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535062341, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535062746, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/14485061826818414241.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1754817535063115, "dur": 407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535063522, "dur": 438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535063961, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535064511, "dur": 1590, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ai.navigation@39ae74efb85f\\Editor\\Updater\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 4, "ts": 1754817535064281, "dur": 2028, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535066310, "dur": 334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535066645, "dur": 743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535067392, "dur": 1009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535068402, "dur": 815, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535069218, "dur": 826, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535070045, "dur": 714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535070760, "dur": 816, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535071577, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535072316, "dur": 1030, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535073349, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754817535073632, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535073703, "dur": 23136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754817535096841, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535097058, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754817535097387, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535097477, "dur": 7883, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754817535105363, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535105605, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754817535105951, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535106011, "dur": 1621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754817535107635, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535107814, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754817535108139, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535108209, "dur": 277, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754817535108488, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535108564, "dur": 1005, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754817535109572, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535109791, "dur": 855, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754817535110648, "dur": 364, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535111040, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754817535111445, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535111541, "dur": 1092, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754817535112636, "dur": 326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535112984, "dur": 791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754817535113775, "dur": 241, "ph": "X", "name": "CheckPristineOutputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754817535114019, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535114190, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535114277, "dur": 11935, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754817535126214, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535126419, "dur": 312, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754817535126732, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535126797, "dur": 31482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754817535158281, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535158451, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754817535158637, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535158727, "dur": 138, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Flow.Editor.ref.dll_6AF951BE66A4493E.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754817535158872, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754817535159283, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535159348, "dur": 125, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754817535159477, "dur": 3021, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754817535162499, "dur": 322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535162843, "dur": 6651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754817535169496, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535169623, "dur": 200, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754817535169837, "dur": 16794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754817535186633, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535186817, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754817535187135, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535187208, "dur": 333, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754817535187542, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535187610, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754817535187961, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754817535188284, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535188352, "dur": 260, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754817535188613, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535188672, "dur": 795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754817535189468, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535189609, "dur": 820, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754817535190430, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535190563, "dur": 809, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/RTLTMPro-Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754817535191373, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535191508, "dur": 823, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754817535192332, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535192472, "dur": 844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754817535193317, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535193473, "dur": 13671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Addressables.DocExampleCode.Editor.Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754817535207146, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535207343, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754817535207616, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535207698, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1754817535208020, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535208107, "dur": 2169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754817535210279, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535210439, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754817535210641, "dur": 5243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1754817535215886, "dur": 811, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535216723, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535218849, "dur": 239992, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817535458842, "dur": 717691, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536176536, "dur": 5971, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754817536182509, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536182649, "dur": 262, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754817536182918, "dur": 3843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754817536186763, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536186888, "dur": 3995, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754817536190885, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536191024, "dur": 4142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754817536195168, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536195294, "dur": 6134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754817536201430, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536201556, "dur": 4167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ResourceManager.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754817536205725, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536205859, "dur": 3463, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754817536209323, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536209593, "dur": 4061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754817536213656, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536213826, "dur": 62745, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754817536276579, "dur": 9738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754817536286318, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536286561, "dur": 3832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Addressables.DocExampleCode.Editor.Tests.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754817536290394, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536290662, "dur": 4234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754817536294907, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536295060, "dur": 4784, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754817536299846, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536299998, "dur": 3742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754817536303742, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536303884, "dur": 3586, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754817536307472, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536307763, "dur": 3773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754817536311539, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536311749, "dur": 4053, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/RTLTMPro-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754817536315805, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536315985, "dur": 78054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754817536394040, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536394139, "dur": 4122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754817536398264, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536398445, "dur": 4888, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1754817536403334, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536403983, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536404103, "dur": 128, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 4, "ts": 1754817536404292, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536404408, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536404500, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/RTLTMPro.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1754817536404560, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536404698, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536404870, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536405008, "dur": 96, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Burst.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1754817536405167, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1754817536405224, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.ResourceManager.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1754817536405311, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536405569, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536405646, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536405831, "dur": 3965, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817536409850, "dur": 6189419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817542599336, "dur": 343, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\SmartVertex.Tools.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1754817542599273, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/SmartVertex.Tools.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1754817542599697, "dur": 98, "ph": "X", "name": "EmitNodeStart", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817542608432, "dur": 113, "ph": "X", "name": "EmitNodeFinish", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1754817542599837, "dur": 8726, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/SmartVertex.Tools.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1754817542608613, "dur": 34743, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1754817542683567, "dur": 10131, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 12192, "tid": 87107, "ts": 1754817542877053, "dur": 41050, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 12192, "tid": 87107, "ts": 1754817542918217, "dur": 25453, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 12192, "tid": 87107, "ts": 1754817542840194, "dur": 119000, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}