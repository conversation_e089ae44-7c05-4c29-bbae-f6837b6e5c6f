using UnityEngine;

namespace SmartVertex.Tools
{
    /// <summary>
    /// MonoBehaviour for testing GeminiAPI functionality in the Unity Editor.
    /// </summary>
    public class GeminiAPITester : MonoBehaviour
    {
        [Header("Gemini API Settings")]
        [SerializeField] private string apiKey;
        [SerializeField] private string model = "gemini-2.5-flash";
        [SerializeField] private string inputText = "Explain how AI works in simple terms.";
        [SerializeField] private string systemInstruction = "You are a helpful AI assistant.";
        [SerializeField] private bool generateOnEnable = true;

        [Header("Generation Configuration")]
        [SerializeField] private float temperature = 1.0f;
        [SerializeField] private float topP = 0.95f;
        [SerializeField] private int topK = 40;
        /// <summary>
        /// Thinking budget configuration. 0=off, -1=dynamic, >0=specific budget.
        /// </summary>
        [SerializeField] private int thinkingBudget = 0;

        private void OnEnable()
        {
            if (generateOnEnable)
                GenerateContent();
        }

        /// <summary>
        /// Triggers content generation. Can be called from the Unity Editor context menu.
        /// </summary>
        [ContextMenu("Generate Content")]
        public async void GenerateContent()
        {
            if (string.IsNullOrEmpty(apiKey))
            {
                Debug.LogError("Please set your Gemini API key!");
                return;
            }

            Debug.Log("Generating content with Gemini API...");

            var request = new GeminiAPI.GeminiRequest
            {
                system_instruction = new GeminiAPI.SystemInstruction
                {
                    parts = new GeminiAPI.Part[]
                    {
                        new GeminiAPI.Part { text = systemInstruction }
                    }
                },
                contents = new GeminiAPI.Content[]
                {
                    new GeminiAPI.Content
                    {
                        parts = new GeminiAPI.Part[]
                        {
                            new GeminiAPI.Part { text = inputText }
                        }
                    }
                },
                generationConfig = new GeminiAPI.GenerationConfig
                {
                    temperature = temperature,
                    topP = topP,
                    topK = topK,
                    thinkingConfig = thinkingBudget != 0 ? new GeminiAPI.ThinkingConfig { thinkingBudget = thinkingBudget } : null
                }
            };

            string apiUrl = $"https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent";
            var geminiService = new GeminiAPI(request, apiKey, apiUrl);

            var response = await geminiService.GenerateContent();
            if (response != null)
            {
                string generatedText = GeminiAPI.ExtractTextFromResponse(response);
                Debug.Log($"Generated Content: {generatedText}");

                // Log usage information
                if (response.usageMetadata != null)
                {
                    Debug.Log($"Token Usage - Prompt: {response.usageMetadata.promptTokenCount}, " +
                             $"Response: {response.usageMetadata.candidatesTokenCount}, " +
                             $"Total: {response.usageMetadata.totalTokenCount}");
                }

                // Log finish reason
                if (response.candidates != null && response.candidates.Length > 0)
                {
                    Debug.Log($"Finish Reason: {response.candidates[0].finishReason}");
                }
            }
            else
            {
                Debug.LogError("Failed to generate content.");
            }
        }

        /// <summary>
        /// Generates simple text without system instruction. Can be called from the Unity Editor context menu.
        /// </summary>
        [ContextMenu("Generate Simple Text")]
        public async void GenerateSimpleText()
        {
            if (string.IsNullOrEmpty(apiKey))
            {
                Debug.LogError("Please set your Gemini API key!");
                return;
            }

            Debug.Log("Generating simple text with Gemini API...");

            var request = GeminiAPI.CreateSimpleTextRequest(inputText);
            string apiUrl = $"https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent";
            var geminiService = new GeminiAPI(request, apiKey, apiUrl);

            var response = await geminiService.GenerateContent();
            if (response != null)
            {
                string generatedText = GeminiAPI.ExtractTextFromResponse(response);
                Debug.Log($"Simple Generated Text: {generatedText}");
            }
            else
            {
                Debug.LogError("Failed to generate simple text.");
            }
        }

        /// <summary>
        /// Generates content with thinking mode enabled. Can be called from the Unity Editor context menu.
        /// </summary>
        [ContextMenu("Generate with Thinking")]
        public async void GenerateWithThinking()
        {
            if (string.IsNullOrEmpty(apiKey))
            {
                Debug.LogError("Please set your Gemini API key!");
                return;
            }

            Debug.Log("Generating content with thinking mode...");

            var request = GeminiAPI.CreateRequestWithConfig(
                "Solve this complex problem: What are the key differences between machine learning and deep learning?",
                temperature: 0.7f,
                thinkingBudget: 1024
            );

            string apiUrl = $"https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent";
            var geminiService = new GeminiAPI(request, apiKey, apiUrl);

            var response = await geminiService.GenerateContent();
            if (response != null)
            {
                string generatedText = GeminiAPI.ExtractTextFromResponse(response);
                Debug.Log($"Thinking Mode Response: {generatedText}");
            }
            else
            {
                Debug.LogError("Failed to generate content with thinking mode.");
            }
        }
    }
}
